<template>
  <div class="simple-test">
    <h2>简单测试</h2>
    
    <div class="test-info">
      <h3>面包屑导航说明</h3>
      <p>• 默认状态：无面包屑，显示根级别数据</p>
      <p>• 点击项目：自动添加到面包屑，进入下一级</p>
      <p>• 点击面包屑：快速跳转到对应级别</p>
    </div>
    
    <div class="test-buttons">
      <button @click="testGuangdong">测试选中广东省叶子节点</button>
      <button @click="testBeijing">测试选中北京市叶子节点</button>
      <button @click="testBothCities">测试选中两个叶子节点</button>
      <button @click="testPartialSelection">测试级联半选状态</button>
      <button @click="clearAll">清空选择</button>
    </div>
    
    <div class="selected-values">
      <h3>选中的值</h3>
      <div>{{ selectedValues }}</div>
      <div>数量: {{ selectedValues.length }}</div>
    </div>
    
    <div class="component-test">
      <h3>组件测试</h3>
      <multi-level-tree-select
        v-model="selectedValues"
        :items="testData"
        :multiple="true"
        :level-titles="['省份', '城市', '区县', '街道']"
        @change="handleChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelTreeSelect from '@/components/multi-level-tree-select/index.vue'

const selectedValues = ref([])

const testData = ref([
  {
    text: '广东省',
    value: 'guangdong',
    children: [
      {
        text: '广州市',
        value: 'guangzhou',
        children: [
          {
            text: '天河区',
            value: 'tianhe',
            children: [
              { text: '珠江新城街道', value: 'zhujiangxincheng' }
            ]
          }
        ]
      }
    ]
  },
  {
    text: '北京市',
    value: 'beijing',
    children: [
      {
        text: '北京市',
        value: 'beijing-city',
        children: [
          {
            text: '朝阳区',
            value: 'chaoyang',
            children: [
              { text: '三里屯街道', value: 'sanlitun' }
            ]
          }
        ]
      }
    ]
  }
])

const testGuangdong = () => {
  selectedValues.value = ['zhujiangxincheng'] // 只选中叶子节点
  console.log('手动设置广东省选中:', selectedValues.value)
}

const testBeijing = () => {
  selectedValues.value = ['sanlitun'] // 只选中叶子节点
  console.log('手动设置北京市选中:', selectedValues.value)
}

const testBothCities = () => {
  selectedValues.value = ['zhujiangxincheng', 'sanlitun'] // 选中两个叶子节点
  console.log('选中两个城市的叶子节点:', selectedValues.value)
}

const testPartialSelection = () => {
  selectedValues.value = ['zhujiangxincheng'] // 只选中天河区下的一个街道
  console.log('测试级联半选状态 - 只选中珠江新城街道:', selectedValues.value)
}

const clearAll = () => {
  selectedValues.value = []
  console.log('清空选择:', selectedValues.value)
}

const handleChange = (values) => {
  console.log('组件change事件:', values)
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #e8f4ff;
  border-radius: 4px;
  border-left: 4px solid #1989fa;
}

.test-info h3 {
  margin: 0 0 10px 0;
  color: #1989fa;
  font-size: 16px;
}

.test-info p {
  margin: 5px 0;
  color: #646566;
  font-size: 14px;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.selected-values {
  margin-bottom: 20px;
  padding: 10px;
  background: #e8f4ff;
  border-radius: 4px;
}

.component-test {
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 4px;
}
</style>
