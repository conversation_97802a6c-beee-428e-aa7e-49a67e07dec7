<template>
  <div class="test-wrapper">
    <div class="test-header">
      <h2>Checkbox树形选择测试</h2>
      <p>测试级联选择和半选状态功能</p>
    </div>
    
    <div class="test-section">
      <h3>功能说明</h3>
      <ul class="feature-list">
        <li>✅ 点击文本：展开下一级（不选中）</li>
        <li>✅ 点击checkbox：选中当前项及所有子项</li>
        <li>✅ 选中父级：自动选中所有子级</li>
        <li>✅ 取消子级：自动取消父级选中</li>
        <li>✅ 部分子级选中：父级显示半选状态</li>
      </ul>
    </div>

    <div class="test-section">
      <h3>测试组件</h3>
      <div class="demo-item">
        <multi-level-tree-select
          v-model="selectedValues"
          :items="testData"
          :multiple="true"
          :level-titles="['省份', '城市', '区县', '街道']"
          @change="handleChange"
          @select="handleSelect"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>选中结果</h3>
      <div class="result-panel">
        <div class="result-item">
          <strong>选中的值：</strong>
          <div class="value-list">
            <span v-for="value in selectedValues" :key="value" class="value-tag">
              {{ value }}
            </span>
            <span v-if="selectedValues.length === 0" class="empty-text">暂无选中项</span>
          </div>
        </div>
        <div class="result-item">
          <strong>选中数量：</strong> {{ selectedValues.length }}
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>操作日志</h3>
      <div class="log-panel">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type" :class="log.type">{{ log.type }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="empty-log">暂无操作记录</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelTreeSelect from '@/components/multi-level-tree-select/index.vue'

defineOptions({
  name: 'CheckboxTreeTest'
})

const selectedValues = ref([])
const logs = ref([])

// 测试数据
const testData = ref([
  {
    text: '广东省',
    value: 'guangdong',
    children: [
      {
        text: '广州市',
        value: 'guangzhou',
        children: [
          {
            text: '天河区',
            value: 'tianhe',
            children: [
              { text: '珠江新城街道', value: 'zhujiangxincheng' },
              { text: '石牌街道', value: 'shipai' },
              { text: '冼村街道', value: 'xiancun' }
            ]
          },
          {
            text: '越秀区',
            value: 'yuexiu',
            children: [
              { text: '北京街道', value: 'beijing-street' },
              { text: '六榕街道', value: 'liurong' }
            ]
          }
        ]
      },
      {
        text: '深圳市',
        value: 'shenzhen',
        children: [
          {
            text: '南山区',
            value: 'nanshan',
            children: [
              { text: '南头街道', value: 'nantou' },
              { text: '沙河街道', value: 'shahe' }
            ]
          },
          {
            text: '福田区',
            value: 'futian',
            children: [
              { text: '华强北街道', value: 'huaqiangbei' },
              { text: '福田街道', value: 'futian-street' }
            ]
          }
        ]
      }
    ]
  },
  {
    text: '北京市',
    value: 'beijing',
    children: [
      {
        text: '北京市',
        value: 'beijing-city',
        children: [
          {
            text: '朝阳区',
            value: 'chaoyang',
            children: [
              { text: '三里屯街道', value: 'sanlitun' },
              { text: '建外街道', value: 'jianwai' }
            ]
          },
          {
            text: '海淀区',
            value: 'haidian',
            children: [
              { text: '中关村街道', value: 'zhongguancun' },
              { text: '学院路街道', value: 'xueyuanlu' }
            ]
          }
        ]
      }
    ]
  }
])

// 添加日志
const addLog = (type, message) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  logs.value.unshift({
    time,
    type,
    message
  })
  
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const handleChange = (values) => {
  addLog('CHANGE', `选中值变化: [${values.join(', ')}] (共${values.length}项)`)
}

const handleSelect = (item, levelIndex) => {
  addLog('SELECT', `展开项目: ${item.text} (第${levelIndex + 1}级)`)
}
</script>

<style lang="scss" scoped>
.test-wrapper {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #323233;
      font-size: 24px;
      margin: 0 0 10px 0;
    }

    p {
      color: #646566;
      font-size: 14px;
      margin: 0;
    }
  }

  .test-section {
    margin-bottom: 30px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #323233;
      font-size: 18px;
      margin: 0 0 15px 0;
      border-bottom: 2px solid #1989fa;
      padding-bottom: 8px;
    }

    .feature-list {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #646566;
        font-size: 14px;
      }
    }

    .demo-item {
      margin-bottom: 20px;
    }

    .result-panel {
      .result-item {
        margin-bottom: 15px;
        
        strong {
          color: #323233;
          font-size: 14px;
        }

        .value-list {
          margin-top: 8px;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .value-tag {
            background: #e8f4ff;
            color: #1989fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid #1989fa;
          }

          .empty-text {
            color: #969799;
            font-style: italic;
          }
        }
      }
    }

    .log-panel {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ebedf0;
      border-radius: 6px;
      padding: 10px;

      .log-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 6px 0;
        border-bottom: 1px solid #f7f8fa;
        font-size: 12px;

        &:last-child {
          border-bottom: none;
        }

        .log-time {
          color: #969799;
          font-family: monospace;
          min-width: 60px;
        }

        .log-type {
          padding: 2px 6px;
          border-radius: 3px;
          font-weight: 500;
          min-width: 60px;
          text-align: center;

          &.CHANGE {
            background: #e8f4ff;
            color: #1989fa;
          }

          &.SELECT {
            background: #fff7e6;
            color: #ff8f1f;
          }
        }

        .log-message {
          color: #323233;
          flex: 1;
        }
      }

      .empty-log {
        text-align: center;
        color: #969799;
        font-style: italic;
        padding: 20px;
      }
    }
  }
}
</style>
