<script setup>
import {ref, onActivated} from "vue";
import {useRouter} from "vue-router";
import {axiosGet} from "@/commonjs/axiosUtils";
import {getUserInfo} from "@/commonjs/accountUtils";
import WeekSlider from "@/components/weekSlider/index.vue";
import moment from "moment";
import {useStore} from "vuex";

defineOptions({
  name: 'home'
})

const store = useStore();
const userInfo = getUserInfo();
const classType = ref(null)
const wrapperRef = ref(null)
const scrollTop = ref(0)
const classList = ref([])
const spaceIds = ref([])
const classTree = ref([])
const treeList = ref([])

const classTypeOps=[
  { text: '全部班级', value: null },
  { text: '教学班', value: 1 },
  { text: '行政班', value: 0 },
]

const date = ref(moment().format('YYYY-MM-DD'))

const roleMap = {
  0: {
    name: '老师',
    url: '/mobile/course/getTeacherCourseList'
  },
  1: {
    name: '管理员',
    url: '/mobile/course/getAllCourseList'
  }
}


const handleScroll = (event) => {
  const target = event.target
  scrollTop.value = target.scrollTop
}

const currentRole = ref(store.state.currentRole || 0)

const roleChange = (val) => {
  currentRole.value = val
  store.commit('SET_CURRENT_ROLE', val)
  getListData()
  showRole.value = false
}

const router = useRouter()
const defaultDate = ref(undefined)
const showRole = ref(false)
const list = ref([])

const dateClickhandler = (row) => {
  date.value = row
  getListData()
}

function itemClick(val) {
  router.push({
    name: 'detail',
    query: {
      date: date.value,
      bean: JSON.stringify(val)
    }
  })
}

function editPasswordClick() {
  router.push({
    name: 'editPassword'
  })
}

const getListData = async () => {
  if (currentRole.value === 1 && classTree.value.length === 0) {
    getClassTreeList()
    getTreeList()
  }
  const timeList = date.value.split('-').map(Number)
  let {code, data} = await axiosGet(roleMap[currentRole.value].url, {
    year: timeList[0],
    month: timeList[1],
    day: timeList[2],
    week: moment(date.value).day()
  })
  if (code === 200) {
    list.value = data
  }
}

onActivated(() => {
  wrapperRef.value.scrollTop = scrollTop.value
  getListData()
})


async function getClassTreeList() {
  const {code, data} = await axiosGet('/manage/class/getClassTreeList')
  if (code === 200) {
    classTree.value = data
  }
}

async function getTreeList() {
  const {code, data} = await axiosGet('/device/getCampusSpaceTreeList')
  if (code === 200) {
    treeList.value = data
  }
}


getListData()
</script>

<template>
  <div class="wrapper" ref="wrapperRef" @scroll="handleScroll">
    <div class="top-role-wrapper" @click="showRole=true">
      <div class="role-name">{{ roleMap[currentRole].name }}</div>
      <van-icon name="arrow-down"/>
    </div>

    <div class="list-wrapper">
      <div class="user-info">
        <div class="name">{{ userInfo.userName }}</div>
        <div class="phone">{{ userInfo.mobile }}</div>
        <div class="password" @click="editPasswordClick">课堂密码</div>
      </div>

      <div class="desc-text">课程点名查看</div>

      <van-sticky offset-top="28">
        <week-slider
            @dateClick="dateClickhandler"
            :showYear="true"
            v-model:defaultDate="defaultDate"/>

        <van-dropdown-menu>
          <van-dropdown-item v-model="classType" :options="classTypeOps" />
          <van-dropdown-item v-model="classType" :options="classTypeOps" />
          <van-dropdown-item>
            <van-tree-select
                v-model:active-id="spaceIds"
                :items="treeList"
            />
            <template #title>
              全部场地
            </template>
          </van-dropdown-item>
        </van-dropdown-menu>

      </van-sticky>

      <div class="item"
           @click="itemClick(item)"
           v-for="(item,index) in list" :key="index">
        <div class="class-name">{{ item.className }}</div>
        <div class="second-text">学科：{{ item.subject }}</div>
        <div class="second-text">教学场地：{{ item.spaceName }}</div>
        <div class="second-text">上课节次：第{{ item.lesson }}节</div>
        <div class="second-text">节次时间：{{
            moment(item.startTime).format('HH:mm')
          }}-{{ moment(item.endTime).format('HH:mm') }}
        </div>
        <div class="second-text">
          学生人数：{{ item.enrolledStudents + item.studentsPresent + item.excusedAbsence + item.truancy }}
        </div>
        <div class="data-static">
          <div class="data">
            应到人数：<span style="color:#0054AD">{{ item.enrolledStudents }}</span>
          </div>
          <div class="data">
            实到人数：<span style="color:#00E33D">{{ item.studentsPresent }}</span>
          </div>
          <div class="data">
            请假人数：<span style="color:#F0BD05">{{ item.excusedAbsence }}</span>
          </div>
          <div class="data">
            缺勤人数：<span style="color:#ED1202">{{ item.truancy }}</span>
          </div>
        </div>
        <div class="look-detail">
          查看详情
          <van-icon name="arrow"/>
        </div>
      </div>
    </div>
  </div>
  <van-popup v-model:show="showRole" position="bottom" round style="height: 400px">
    <div class="role-wrapper">
      <div class="role-title">选择角色</div>
      <div class="role-item"
           @click="roleChange(1)"
           v-if="userInfo.isManager === 1">管理员
      </div>
      <div class="role-item"
           @click="roleChange(0)"
      >教师
      </div>
      <div style="flex: 1"></div>
      <van-button type="primary" block v-if="false" class="">确认</van-button>

    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;


  .top-role-wrapper {
    display: flex;
    box-sizing: border-box;
    height: 32px;
    padding: 0 29px;
    align-items: center;
    color: var(--theme-color);

    .role-name {
      font-size: 13px;
      font-weight: 500;
      margin-right: 10px;
    }
  }

  .list-wrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0 9px;
    height: calc(100% - 32px);
    overflow-y: auto;

    .user-info {
      width: 100%;
      display: flex;
      border-radius: 13px;
      box-sizing: border-box;
      padding: 0 14px 0 23px;
      background: #FFFFFF;
      align-items: center;
      height: 70px;

      .name {
        font-size: 18px;
        font-weight: 700;
        color: rgba(61, 61, 61, 1);
      }

      .phone {
        flex: 1;
        padding: 0 13px;
        font-size: 16px;
        font-weight: 400;
        color: #808080;
      }

      .password {
        color: var(--theme-color);
      }
    }

    .desc-text {
      font-size: 12px;
      height: 34px;
      box-sizing: border-box;
      padding-top: 6px;
      font-weight: 400;
      color: rgba(158, 157, 157, 1);
    }

    .item {
      margin-top: 8px;
      box-sizing: border-box;
      padding: 23px 18px 0;
      background-color: white;
      border-radius: 10px;

      .class-name {
        font-size: 18.35px;
        font-weight: 700;
        color: rgba(0, 0, 0, 100);
      }

      .second-text {
        margin-top: 10px;
        font-size: 16.06px;
        font-weight: 400;
        color: rgba(134, 150, 187, 1);
      }

      .data-static {
        display: flex;
        flex-wrap: wrap;

        .data {
          margin-top: 10px;
          font-size: 16.06px;
          font-weight: 400;
          color: rgba(134, 150, 187, 1);
          flex: 0 0 50%;
        }
      }

      .look-detail {
        width: 100%;
        height: 50px;
        margin-top: 6px;
        line-height: 50px;
        text-align: center;
        border-top: 1px solid #E5E5E5;
        color: var(--theme-color);
      }
    }
  }
}

.role-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  box-sizing: border-box;
  padding: 14px;
  background-color: #f9f9f9;

  .role-title {
    margin-bottom: 80px;
    font-size: 14.69px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 14.93px;
    color: rgba(26, 26, 26, 1);
    text-align: center;
    vertical-align: top;
  }

  .role-item {
    margin: 10px;
    width: 240px;
    line-height: 40px;
    text-align: center;
    color: var(--theme-color);
    background-color: rgba(var(--theme-color-rgb), 0.15);
  }

}
</style>
