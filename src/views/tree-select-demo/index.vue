<template>
  <div class="demo-wrapper">
    <div class="demo-header">
      <h2>多级树形选择组件演示</h2>
    </div>
    
    <div class="demo-section">
      <h3>4级树形结构演示（点击展开，checkbox选择）</h3>
      <div class="demo-item">
        <multi-level-tree-select
          v-model="selectedValues1"
          :items="treeData4Level"
          :multiple="true"
          :level-titles="['省份', '城市', '区县', '街道']"
          @change="handleChange1"
        />
      </div>
      <div class="result">
        <p>选中的值: {{ selectedValues1 }}</p>
        <p>选中数量: {{ selectedValues1.length }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>6级树形结构演示（支持横向滚动）</h3>
      <div class="demo-item">
        <multi-level-tree-select
          v-model="selectedValues2"
          :items="treeData6Level"
          :multiple="true"
          :level-titles="['国家', '省份', '城市', '区县', '街道', '社区']"
          :level-width="150"
          @change="handleChange2"
        />
      </div>
      <div class="result">
        <p>选中的值: {{ selectedValues2 }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>单选模式演示</h3>
      <div class="demo-item">
        <multi-level-tree-select
          v-model="selectedValues3"
          :items="treeData4Level"
          :multiple="false"
          :level-titles="['省份', '城市', '区县', '街道']"
          @change="handleChange3"
        />
      </div>
      <div class="result">
        <p>选中的值: {{ selectedValues3 }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelTreeSelect from '@/components/multi-level-tree-select/index.vue'

defineOptions({
  name: 'TreeSelectDemo'
})

const selectedValues1 = ref([])
const selectedValues2 = ref([])
const selectedValues3 = ref([])

// 4级树形数据
const treeData4Level = ref([
  {
    text: '广东省',
    value: 'guangdong',
    children: [
      {
        text: '广州市',
        value: 'guangzhou',
        children: [
          {
            text: '天河区',
            value: 'tianhe',
            children: [
              { text: '珠江新城街道', value: 'zhujiangxincheng' },
              { text: '石牌街道', value: 'shipai' },
              { text: '冼村街道', value: 'xiancun' }
            ]
          },
          {
            text: '越秀区',
            value: 'yuexiu',
            children: [
              { text: '北京街道', value: 'beijing' },
              { text: '六榕街道', value: 'liurong' }
            ]
          }
        ]
      },
      {
        text: '深圳市',
        value: 'shenzhen',
        children: [
          {
            text: '南山区',
            value: 'nanshan',
            children: [
              { text: '南头街道', value: 'nantou' },
              { text: '沙河街道', value: 'shahe' }
            ]
          }
        ]
      }
    ]
  },
  {
    text: '北京市',
    value: 'beijing',
    children: [
      {
        text: '北京市',
        value: 'beijing-city',
        children: [
          {
            text: '朝阳区',
            value: 'chaoyang',
            children: [
              { text: '三里屯街道', value: 'sanlitun' },
              { text: '建外街道', value: 'jianwai' }
            ]
          },
          {
            text: '海淀区',
            value: 'haidian',
            children: [
              { text: '中关村街道', value: 'zhongguancun' },
              { text: '学院路街道', value: 'xueyuanlu' }
            ]
          }
        ]
      }
    ]
  }
])

// 6级树形数据
const treeData6Level = ref([
  {
    text: '中国',
    value: 'china',
    children: [
      {
        text: '广东省',
        value: 'guangdong',
        children: [
          {
            text: '广州市',
            value: 'guangzhou',
            children: [
              {
                text: '天河区',
                value: 'tianhe',
                children: [
                  {
                    text: '珠江新城街道',
                    value: 'zhujiangxincheng',
                    children: [
                      { text: '花城社区', value: 'huacheng' },
                      { text: '冼村社区', value: 'xiancun-community' },
                      { text: '潭村社区', value: 'tancun' }
                    ]
                  },
                  {
                    text: '石牌街道',
                    value: 'shipai',
                    children: [
                      { text: '石牌社区', value: 'shipai-community' },
                      { text: '暨大社区', value: 'jida' }
                    ]
                  }
                ]
              },
              {
                text: '越秀区',
                value: 'yuexiu',
                children: [
                  {
                    text: '北京街道',
                    value: 'beijing-street',
                    children: [
                      { text: '盘福社区', value: 'panfu' },
                      { text: '万福社区', value: 'wanfu' }
                    ]
                  }
                ]
              }
            ]
          },
          {
            text: '深圳市',
            value: 'shenzhen',
            children: [
              {
                text: '南山区',
                value: 'nanshan',
                children: [
                  {
                    text: '南头街道',
                    value: 'nantou',
                    children: [
                      { text: '南头社区', value: 'nantou-community' },
                      { text: '田厦社区', value: 'tianxia' }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        text: '北京市',
        value: 'beijing-province',
        children: [
          {
            text: '北京市',
            value: 'beijing-city',
            children: [
              {
                text: '朝阳区',
                value: 'chaoyang',
                children: [
                  {
                    text: '三里屯街道',
                    value: 'sanlitun',
                    children: [
                      { text: '三里屯社区', value: 'sanlitun-community' },
                      { text: '幸福社区', value: 'xingfu' }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])

const handleChange1 = (values) => {
  console.log('4级树选择变化:', values)
}

const handleChange2 = (values) => {
  console.log('6级树选择变化:', values)
}

const handleChange3 = (values) => {
  console.log('单选模式变化:', values)
}
</script>

<style lang="scss" scoped>
.demo-wrapper {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .demo-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #323233;
      font-size: 24px;
      margin: 0;
    }
  }

  .demo-section {
    margin-bottom: 40px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #323233;
      font-size: 18px;
      margin: 0 0 20px 0;
      border-bottom: 2px solid #1989fa;
      padding-bottom: 10px;
    }

    .demo-item {
      margin-bottom: 20px;
    }

    .result {
      padding: 15px;
      background: #f7f8fa;
      border-radius: 6px;
      border-left: 4px solid #1989fa;

      p {
        margin: 0;
        color: #646566;
        font-size: 14px;
        word-break: break-all;
      }
    }
  }
}
</style>
