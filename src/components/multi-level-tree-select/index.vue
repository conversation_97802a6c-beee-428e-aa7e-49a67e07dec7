<template>
  <div class="multi-level-tree-select">
    <div 
      class="tree-container" 
      :class="{ 'scrollable': levels.length > 4 }"
      ref="treeContainer"
    >
      <div 
        class="tree-level" 
        v-for="(level, levelIndex) in levels" 
        :key="levelIndex"
        :style="getLevelStyle(levelIndex)"
      >
        <div class="level-header">
          {{ getLevelTitle(levelIndex) }}
        </div>
        <div class="level-content">
          <div 
            class="tree-item"
            v-for="item in level.items"
            :key="item.value"
            :class="{ 
              'selected': isSelected(item.value),
              'has-children': item.children && item.children.length > 0
            }"
            @click="handleItemClick(item, levelIndex)"
          >
            <div class="item-content">
              <span class="item-text">{{ item.text }}</span>
              <van-icon 
                v-if="isSelected(item.value)" 
                name="success" 
                class="selected-icon"
              />
              <van-icon 
                v-if="item.children && item.children.length > 0" 
                name="arrow" 
                class="arrow-icon"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 选中项显示 -->
    <div class="selected-items" v-if="selectedItems.length > 0">
      <div class="selected-header">已选择 ({{ selectedItems.length }})</div>
      <div class="selected-list">
        <van-tag 
          v-for="item in selectedItems" 
          :key="item.value"
          closeable
          @close="removeSelected(item.value)"
          class="selected-tag"
        >
          {{ item.text }}
        </van-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  // 树形数据
  items: {
    type: Array,
    default: () => []
  },
  // 选中的值
  modelValue: {
    type: Array,
    default: () => []
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 每级的默认宽度（当超过4级时使用）
  levelWidth: {
    type: Number,
    default: 120
  },
  // 级别标题
  levelTitles: {
    type: Array,
    default: () => ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'select'])

const treeContainer = ref(null)
const selectedValues = ref([...props.modelValue])
const levels = ref([])

// 初始化第一级
const initLevels = () => {
  levels.value = [{
    items: props.items,
    parentPath: []
  }]
}

// 计算选中的项目详情
const selectedItems = computed(() => {
  const items = []
  const findItems = (nodes, path = []) => {
    for (const node of nodes) {
      if (selectedValues.value.includes(node.value)) {
        items.push({
          ...node,
          path: [...path, node.value]
        })
      }
      if (node.children && node.children.length > 0) {
        findItems(node.children, [...path, node.value])
      }
    }
  }
  findItems(props.items)
  return items
})

// 获取级别样式
const getLevelStyle = (levelIndex) => {
  const totalLevels = levels.value.length
  if (totalLevels <= 4) {
    return {
      width: `${100 / totalLevels}%`,
      minWidth: 'auto'
    }
  } else {
    return {
      width: `${props.levelWidth}px`,
      minWidth: `${props.levelWidth}px`
    }
  }
}

// 获取级别标题
const getLevelTitle = (levelIndex) => {
  return props.levelTitles[levelIndex] || `第${levelIndex + 1}级`
}

// 判断是否选中
const isSelected = (value) => {
  return selectedValues.value.includes(value)
}

// 处理项目点击
const handleItemClick = (item, levelIndex) => {
  // 处理选中状态
  if (props.multiple) {
    const index = selectedValues.value.indexOf(item.value)
    if (index > -1) {
      selectedValues.value.splice(index, 1)
    } else {
      selectedValues.value.push(item.value)
    }
  } else {
    selectedValues.value = [item.value]
  }
  
  // 如果有子级，展开下一级
  if (item.children && item.children.length > 0) {
    // 移除当前级别之后的所有级别
    levels.value = levels.value.slice(0, levelIndex + 1)
    
    // 添加新的级别
    levels.value.push({
      items: item.children,
      parentPath: [...levels.value[levelIndex].parentPath, item.value]
    })
    
    // 滚动到新级别
    nextTick(() => {
      scrollToLevel(levelIndex + 1)
    })
  }
  
  // 发出事件
  emit('update:modelValue', selectedValues.value)
  emit('change', selectedValues.value)
  emit('select', item, levelIndex)
}

// 移除选中项
const removeSelected = (value) => {
  const index = selectedValues.value.indexOf(value)
  if (index > -1) {
    selectedValues.value.splice(index, 1)
    emit('update:modelValue', selectedValues.value)
    emit('change', selectedValues.value)
  }
}

// 滚动到指定级别
const scrollToLevel = (levelIndex) => {
  if (!treeContainer.value || levels.value.length <= 4) return
  
  const scrollLeft = levelIndex * props.levelWidth
  treeContainer.value.scrollTo({
    left: scrollLeft,
    behavior: 'smooth'
  })
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedValues.value = [...newVal]
}, { deep: true })

// 监听items变化
watch(() => props.items, () => {
  initLevels()
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.multi-level-tree-select {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  
  .tree-container {
    display: flex;
    min-height: 300px;
    max-height: 400px;
    
    &.scrollable {
      overflow-x: auto;
      overflow-y: hidden;
      
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    }
    
    .tree-level {
      flex-shrink: 0;
      border-right: 1px solid #ebedf0;
      display: flex;
      flex-direction: column;
      
      &:last-child {
        border-right: none;
      }
      
      .level-header {
        padding: 12px 16px;
        background: #f7f8fa;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        border-bottom: 1px solid #ebedf0;
        text-align: center;
      }
      
      .level-content {
        flex: 1;
        overflow-y: auto;
        
        .tree-item {
          padding: 0;
          border-bottom: 1px solid #ebedf0;
          cursor: pointer;
          transition: background-color 0.2s;
          
          &:hover {
            background: #f7f8fa;
          }
          
          &.selected {
            background: #e8f4ff;
            
            .item-text {
              color: #1989fa;
              font-weight: 500;
            }
          }
          
          &:last-child {
            border-bottom: none;
          }
          
          .item-content {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            
            .item-text {
              flex: 1;
              font-size: 14px;
              color: #323233;
              line-height: 20px;
            }
            
            .selected-icon {
              color: #1989fa;
              font-size: 16px;
              margin-left: 8px;
            }
            
            .arrow-icon {
              color: #969799;
              font-size: 12px;
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
  
  .selected-items {
    border-top: 1px solid #ebedf0;
    padding: 12px 16px;
    background: #f7f8fa;
    
    .selected-header {
      font-size: 14px;
      color: #646566;
      margin-bottom: 8px;
    }
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .selected-tag {
        margin: 0;
      }
    }
  }
}
</style>
