<template>
  <div class="multi-level-tree-select">
    <div
      class="tree-container"
      :class="{ 'scrollable': levels.length > 3 }"
      ref="treeContainer"
    >
      <div 
        class="tree-level" 
        v-for="(level, levelIndex) in levels" 
        :key="levelIndex"
        :style="getLevelStyle(levelIndex)"
      >
        <div class="level-header">
          {{ getLevelTitle(levelIndex) }}
        </div>
        <div class="level-content">
          <div
            class="tree-item"
            v-for="item in level.items"
            :key="item.value"
            :class="{
              'has-children': item.children && item.children.length > 0
            }"
          >
            <div class="item-content">
              <div class="item-main" @click="handleItemClick(item, levelIndex)">
                <span class="item-text">{{ item.text }}</span>
              </div>
              <van-checkbox
                :model-value="getCheckboxState(item.value)"
                :indeterminate="isIndeterminate(item.value)"
                @click.stop
                @update:model-value="handleCheckboxChange(item, $event)"
                class="item-checkbox"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 选中项显示 -->
    <div class="selected-items" v-if="selectedItems.length > 0">
      <div class="selected-header">已选择 ({{ selectedItems.length }})</div>
      <div class="selected-list">
        <van-tag 
          v-for="item in selectedItems" 
          :key="item.value"
          closeable
          @close="removeSelected(item.value)"
          class="selected-tag"
        >
          {{ item.text }}
        </van-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  // 树形数据
  items: {
    type: Array,
    default: () => []
  },
  // 选中的值
  modelValue: {
    type: Array,
    default: () => []
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 每级的默认宽度（当超过4级时使用）
  levelWidth: {
    type: Number,
    default: 120
  },
  // 级别标题
  levelTitles: {
    type: Array,
    default: () => ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'select'])

const treeContainer = ref(null)
const selectedValues = ref([...props.modelValue])
const levels = ref([])

// 初始化第一级
const initLevels = () => {
  levels.value = [{
    items: props.items,
    parentPath: []
  }]
}

// 计算选中的项目详情
const selectedItems = computed(() => {
  const items = []
  const findItems = (nodes, path = []) => {
    for (const node of nodes) {
      if (selectedValues.value.includes(node.value)) {
        items.push({
          ...node,
          path: [...path, node.value]
        })
      }
      if (node.children && node.children.length > 0) {
        findItems(node.children, [...path, node.value])
      }
    }
  }
  findItems(props.items)
  return items
})

// 获取级别样式
const getLevelStyle = (levelIndex) => {
  const totalLevels = levels.value.length
  if (totalLevels <= 3) {
    return {
      width: `${100 / totalLevels}%`,
      minWidth: 'auto'
    }
  } else {
    return {
      width: `${props.levelWidth}px`,
      minWidth: `${props.levelWidth}px`
    }
  }
}

// 获取级别标题
const getLevelTitle = (levelIndex) => {
  return props.levelTitles[levelIndex] || `第${levelIndex + 1}级`
}

// 获取所有子节点的值
const getAllChildrenValues = (node) => {
  const values = []
  const traverse = (item) => {
    values.push(item.value)
    if (item.children && item.children.length > 0) {
      item.children.forEach(child => traverse(child))
    }
  }
  traverse(node)
  return values
}

// 获取所有父节点的值
const getAllParentValues = (targetValue, nodes = props.items, parents = []) => {
  for (const node of nodes) {
    const currentPath = [...parents, node.value]
    if (node.value === targetValue) {
      return parents
    }
    if (node.children && node.children.length > 0) {
      const result = getAllParentValues(targetValue, node.children, currentPath)
      if (result) return result
    }
  }
  return null
}

// 获取节点的所有兄弟节点（包括自己）
const getSiblings = (targetValue, nodes = props.items) => {
  const findSiblings = (items, path = []) => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.value === targetValue) {
        return items.map(sibling => sibling.value)
      }
      if (item.children && item.children.length > 0) {
        const result = findSiblings(item.children, [...path, item.value])
        if (result) return result
      }
    }
    return null
  }
  return findSiblings(nodes) || []
}

// 检查checkbox状态
const getCheckboxState = (value) => {
  // 如果直接被选中，返回true
  if (selectedValues.value.includes(value)) {
    return true
  }

  // 检查是否所有直接子节点都被选中
  const node = findNodeByValue(value)
  if (node && node.children && node.children.length > 0) {
    return node.children.every(child => getCheckboxState(child.value))
  }

  return false
}

// 检查是否为半选状态
const isIndeterminate = (value) => {
  const node = findNodeByValue(value)
  if (!node || !node.children || node.children.length === 0) {
    return false
  }

  // 如果当前节点的checkbox状态为true，不显示半选状态
  if (getCheckboxState(value)) {
    return false
  }

  // 检查直接子节点的选中状态
  const childStates = node.children.map(child => getCheckboxState(child.value))
  const selectedCount = childStates.filter(state => state).length

  // 如果有部分子节点被选中，则显示半选状态
  return selectedCount > 0 && selectedCount < node.children.length
}

// 处理项目点击（只展开，不选中）
const handleItemClick = (item, levelIndex) => {
  // 如果有子级，展开下一级
  if (item.children && item.children.length > 0) {
    // 移除当前级别之后的所有级别
    levels.value = levels.value.slice(0, levelIndex + 1)

    // 添加新的级别
    levels.value.push({
      items: item.children,
      parentPath: [...levels.value[levelIndex].parentPath, item.value]
    })

    // 滚动到新级别
    nextTick(() => {
      scrollToLevel(levelIndex + 1)
    })
  }

  // 发出展开事件
  emit('select', item, levelIndex)
}

// 处理checkbox变化
const handleCheckboxChange = (item, checked) => {
  if (checked) {
    // 选中：只添加叶子节点（没有子节点的节点）
    addLeafNodes(item)
  } else {
    // 取消选中：移除自己和所有子节点
    const allValues = getAllChildrenValues(item)
    allValues.forEach(value => {
      const index = selectedValues.value.indexOf(value)
      if (index > -1) {
        selectedValues.value.splice(index, 1)
      }
    })

    // 取消父节点选中
    updateParentDeselection(item.value)
  }

  // 发出事件
  emit('update:modelValue', selectedValues.value)
  emit('change', selectedValues.value)
}

// 添加叶子节点
const addLeafNodes = (node) => {
  if (!node.children || node.children.length === 0) {
    // 这是叶子节点，直接添加
    if (!selectedValues.value.includes(node.value)) {
      selectedValues.value.push(node.value)
    }
  } else {
    // 这是父节点，递归添加所有叶子节点
    node.children.forEach(child => {
      addLeafNodes(child)
    })
  }
}

// 找到节点对象
const findNodeByValue = (value, nodes = props.items) => {
  for (const node of nodes) {
    if (node.value === value) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByValue(value, node.children)
      if (found) return found
    }
  }
  return null
}



// 更新父节点取消选中状态
const updateParentDeselection = (childValue) => {
  const parents = getAllParentValues(childValue)
  if (!parents || parents.length === 0) return

  // 取消所有父节点的选中状态
  parents.forEach(parentValue => {
    const index = selectedValues.value.indexOf(parentValue)
    if (index > -1) {
      selectedValues.value.splice(index, 1)
    }
  })
}

// 移除选中项
const removeSelected = (value) => {
  const index = selectedValues.value.indexOf(value)
  if (index > -1) {
    selectedValues.value.splice(index, 1)
    emit('update:modelValue', selectedValues.value)
    emit('change', selectedValues.value)
  }
}

// 滚动到指定级别
const scrollToLevel = (levelIndex) => {
  if (!treeContainer.value || levels.value.length <= 3) return

  const scrollLeft = levelIndex * props.levelWidth
  treeContainer.value.scrollTo({
    left: scrollLeft,
    behavior: 'smooth'
  })
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedValues.value = [...newVal]
}, { deep: true })

// 监听items变化
watch(() => props.items, () => {
  initLevels()
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.multi-level-tree-select {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  
  .tree-container {
    display: flex;
    min-height: 300px;
    max-height: 400px;
    
    &.scrollable {
      overflow-x: auto;
      overflow-y: hidden;
      
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    }
    
    .tree-level {
      flex-shrink: 0;
      border-right: 1px solid #ebedf0;
      display: flex;
      flex-direction: column;
      
      &:last-child {
        border-right: none;
      }
      
      .level-header {
        padding: 12px 16px;
        background: #f7f8fa;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        border-bottom: 1px solid #ebedf0;
        text-align: center;
      }
      
      .level-content {
        flex: 1;
        overflow-y: auto;
        
        .tree-item {
          border-bottom: 1px solid #ebedf0;
          transition: background-color 0.2s;

          &:hover {
            background: #f7f8fa;
          }

          &:last-child {
            border-bottom: none;
          }

          .item-content {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

            .item-main {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: space-between;
              cursor: pointer;
              padding: 4px 0;

              .item-text {
                flex: 1;
                font-size: 14px;
                color: #323233;
                line-height: 20px;
              }

              .arrow-icon {
                color: #969799;
                font-size: 12px;
                margin-left: 8px;
              }
            }

            .item-checkbox {
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
  
  .selected-items {
    border-top: 1px solid #ebedf0;
    padding: 12px 16px;
    background: #f7f8fa;
    
    .selected-header {
      font-size: 14px;
      color: #646566;
      margin-bottom: 8px;
    }
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .selected-tag {
        margin: 0;
      }
    }
  }
}
</style>
